#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件防盗保护工具 - Qt6版本
完美实现防盗文件夹结构，确保密码加密有效
"""

import sys
import os
import zipfile
import random
import string
import tempfile
import shutil
from pathlib import Path
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                            QWidget, QPushButton, QLabel, QLineEdit, QTextEdit, 
                            QFileDialog, QMessageBox, QProgressBar, QGroupBox,
                            QRadioButton, QCheckBox, QGridLayout)
from PyQt6.QtCore import QThread, pyqtSignal, Qt
from PyQt6.QtGui import QFont, QIcon


class FileProtector:
    """文件保护核心类"""
    
    @staticmethod
    def generate_random_password(length=12):
        """生成随机密码（避免特殊字符）"""
        characters = string.ascii_letters + string.digits + "!@#$%&"
        return ''.join(random.choice(characters) for _ in range(length))
    
    @staticmethod
    def generate_random_filename():
        """生成随机文件名"""
        return ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(16)) + ".zip"
    
    @staticmethod
    def create_password_protected_zip(file_path, output_path, password):
        """创建密码保护的ZIP文件"""
        # 首先尝试使用pyminizip，如果失败则使用zipfile备用方案

        # 方案1：尝试pyminizip
        try:
            import pyminizip

            # 创建临时目录来处理文件名问题
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)

                if os.path.isfile(file_path):
                    # 单文件 - 复制到临时位置避免路径问题
                    original_name = os.path.basename(file_path)
                    # 创建安全的临时文件名
                    safe_name = "temp_file.tmp"
                    temp_file = temp_path / safe_name

                    # 复制文件
                    shutil.copy2(file_path, temp_file)

                    # 使用pyminizip压缩，指定原始文件名
                    pyminizip.compress(str(temp_file), original_name, output_path, password, 5)

                else:
                    # 文件夹 - 复制到临时位置
                    temp_folder = temp_path / "temp_folder"
                    shutil.copytree(file_path, temp_folder)

                    # 收集所有文件
                    file_list = []
                    prefix_list = []

                    for root, dirs, files in os.walk(temp_folder):
                        for file in files:
                            full_path = os.path.join(root, file)
                            file_list.append(full_path)

                            # 计算相对路径，保持原始结构
                            rel_path = os.path.relpath(full_path, temp_folder)
                            prefix_list.append(os.path.dirname(rel_path) if os.path.dirname(rel_path) != '.' else "")

                    if file_list:
                        pyminizip.compress_multiple(file_list, prefix_list, output_path, password, 5)
                    else:
                        raise Exception("文件夹为空")

            return True, "使用pyminizip强加密成功"

        except ImportError:
            pass  # pyminizip未安装，直接使用备用方案

        except Exception as e:
            # pyminizip失败，记录错误但继续使用备用方案
            print(f"pyminizip失败: {str(e)}, 使用备用加密方案")

        # 方案2：使用zipfile备用方案
        try:
            with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED) as zf:
                if os.path.isfile(file_path):
                    # 单文件
                    with open(file_path, 'rb') as f:
                        data = f.read()

                    # 创建ZipInfo对象以正确处理文件名
                    info = zipfile.ZipInfo(os.path.basename(file_path))
                    info.compress_type = zipfile.ZIP_DEFLATED

                    # 写入加密数据
                    zf.writestr(info, data, pwd=password.encode('utf-8'))

                else:
                    # 文件夹
                    for root, dirs, files in os.walk(file_path):
                        for file in files:
                            full_path = os.path.join(root, file)
                            arc_name = os.path.relpath(full_path, os.path.dirname(file_path))

                            # 读取文件数据
                            with open(full_path, 'rb') as f:
                                data = f.read()

                            # 创建ZipInfo对象
                            info = zipfile.ZipInfo(arc_name)
                            info.compress_type = zipfile.ZIP_DEFLATED

                            # 写入加密数据
                            zf.writestr(info, data, pwd=password.encode('utf-8'))

            return True, "使用zipfile标准加密（建议安装pyminizip获得更强加密）"

        except Exception as e:
            return False, f"所有加密方案都失败: {str(e)}"
    
    @staticmethod
    def create_protected_archive(input_path, output_dir=None):
        """
        创建防盗保护的文件结构
        
        结构：
        原文件名.zip
        └── 原文件名/
            ├── 随机名.zip (密码保护)
            └── 解压密码：xxxxxxx/
                └── 使用说明.txt
        """
        input_path = Path(input_path)
        if not input_path.exists():
            raise FileNotFoundError(f"输入路径不存在: {input_path}")
        
        # 设置输出目录
        if output_dir is None:
            output_dir = input_path.parent
        else:
            output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成随机密码和文件名
        password = FileProtector.generate_random_password()
        inner_zip_name = FileProtector.generate_random_filename()
        
        # 最终输出文件名（不带_protected后缀）
        base_name = input_path.stem if input_path.is_file() else input_path.name
        final_zip_path = output_dir / f"{base_name}.zip"
        
        # 创建临时工作目录
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # 创建主文件夹（以原文件名命名）
            main_folder = temp_path / base_name
            main_folder.mkdir()
            
            # 1. 创建内部加密压缩包
            inner_zip_path = main_folder / inner_zip_name
            success, msg = FileProtector.create_password_protected_zip(
                str(input_path), str(inner_zip_path), password
            )
            
            if not success:
                raise Exception(f"创建加密压缩包失败: {msg}")
            
            # 2. 创建密码提示文件夹
            password_folder_name = f"解压密码：{password}"
            password_folder = main_folder / password_folder_name
            password_folder.mkdir()
            
            # 在密码文件夹中创建说明文件
            readme_path = password_folder / "使用说明.txt"
            with open(readme_path, 'w', encoding='utf-8') as f:
                f.write(f"解压密码：{password}\n\n")
                f.write("使用方法：\n")
                f.write(f"1. 使用上述密码解压文件 {inner_zip_name}\n")
                f.write("2. 即可获得原始文件\n\n")
                f.write("注意：请妥善保管此密码，丢失后无法恢复文件！\n\n")
                f.write("防盗说明：\n")
                f.write("此文件采用双重保护结构，有效防止批量盗取。")
            
            # 3. 创建最终的外层压缩包
            with zipfile.ZipFile(final_zip_path, 'w', zipfile.ZIP_DEFLATED) as final_zip:
                # 添加整个主文件夹
                for file_path in main_folder.rglob('*'):
                    if file_path.is_file():
                        arc_name = file_path.relative_to(temp_path)
                        final_zip.write(file_path, arc_name)
        
        return str(final_zip_path), password, msg


class ProtectionWorker(QThread):
    """文件保护工作线程"""
    
    progress = pyqtSignal(str)  # 进度信息
    finished = pyqtSignal(str, str, str)  # 完成信号：输出路径，密码，加密方式
    error = pyqtSignal(str)  # 错误信号
    
    def __init__(self, input_path, output_dir, batch_mode=False, file_extensions=None):
        super().__init__()
        self.input_path = input_path
        self.output_dir = output_dir
        self.batch_mode = batch_mode
        self.file_extensions = file_extensions or []
    
    def run(self):
        try:
            if self.batch_mode:
                self.batch_protect()
            else:
                self.single_protect()
        except Exception as e:
            self.error.emit(str(e))
    
    def single_protect(self):
        """单文件保护"""
        self.progress.emit(f"开始保护: {os.path.basename(self.input_path)}")
        
        output_path, password, encryption_method = FileProtector.create_protected_archive(
            self.input_path, self.output_dir
        )
        
        self.progress.emit("保护完成！")
        self.finished.emit(output_path, password, encryption_method)
    
    def batch_protect(self):
        """批量保护"""
        input_dir = Path(self.input_path)
        if not input_dir.is_dir():
            raise Exception("批量模式需要选择文件夹")
        
        # 获取要处理的文件
        files_to_process = []
        if self.file_extensions:
            for ext in self.file_extensions:
                files_to_process.extend(input_dir.glob(f"*{ext}"))
        else:
            files_to_process = [f for f in input_dir.iterdir() if f.is_file()]
        
        if not files_to_process:
            raise Exception("没有找到要处理的文件")
        
        self.progress.emit(f"找到 {len(files_to_process)} 个文件，开始批量保护...")
        
        results = []
        failed_files = []

        for i, file_path in enumerate(files_to_process, 1):
            self.progress.emit(f"处理文件 {i}/{len(files_to_process)}: {file_path.name}")

            try:
                output_path, password, encryption_method = FileProtector.create_protected_archive(
                    str(file_path), self.output_dir
                )
                results.append({
                    'file': file_path.name,
                    'output': output_path,
                    'password': password,
                    'encryption': encryption_method
                })
                self.progress.emit(f"✅ {file_path.name} 保护成功")

            except Exception as e:
                error_msg = str(e)
                failed_files.append({
                    'file': file_path.name,
                    'error': error_msg
                })
                self.progress.emit(f"❌ {file_path.name} 失败: {error_msg}")

                # 如果是文件名问题，尝试建议解决方案
                if "error in closing" in error_msg or "error in opening" in error_msg:
                    self.progress.emit(f"💡 建议: {file_path.name} 可能包含特殊字符，请尝试重命名")

                # 继续处理下一个文件，不中断批量处理
                continue
        
        # 生成批量报告
        if results or failed_files:
            report_path = Path(self.output_dir) / "批量保护报告.txt"
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write("批量文件保护报告\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"处理时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"总文件数: {len(files_to_process)}\n")
                f.write(f"成功处理: {len(results)}\n")
                f.write(f"失败文件: {len(failed_files)}\n\n")

                if results:
                    f.write("成功处理的文件:\n")
                    f.write("=" * 30 + "\n")
                    for result in results:
                        f.write(f"原文件: {result['file']}\n")
                        f.write(f"保护文件: {result['output']}\n")
                        f.write(f"解压密码: {result['password']}\n")
                        f.write(f"加密方式: {result['encryption']}\n")
                        f.write("-" * 30 + "\n")

                if failed_files:
                    f.write("\n失败的文件:\n")
                    f.write("=" * 30 + "\n")
                    for failed in failed_files:
                        f.write(f"文件: {failed['file']}\n")
                        f.write(f"错误: {failed['error']}\n")
                        f.write("-" * 30 + "\n")

            success_count = len(results)
            total_count = len(files_to_process)

            self.progress.emit(f"批量保护完成！成功: {success_count}/{total_count}")
            if failed_files:
                self.progress.emit(f"⚠️ {len(failed_files)} 个文件处理失败，请查看报告")
            self.progress.emit(f"详细报告已保存至: {report_path}")

            if results:
                self.finished.emit(str(report_path), f"成功{success_count}/{total_count}个文件", "批量处理")
            else:
                raise Exception("所有文件都处理失败，请检查文件名是否包含特殊字符")
        else:
            raise Exception("没有文件被处理")


class FileProtectorGUI(QMainWindow):
    """主界面"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.worker = None
    
    def init_ui(self):
        self.setWindowTitle("文件防盗保护工具 🛡️")
        self.setGeometry(100, 100, 700, 600)
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("文件防盗保护工具 🛡️")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        title.setStyleSheet("color: #2196F3; margin: 10px;")
        layout.addWidget(title)
        
        # 说明
        desc = QLabel("保护你的文件不被批量盗取 💪")
        desc.setAlignment(Qt.AlignmentFlag.AlignCenter)
        desc.setStyleSheet("color: #666; margin-bottom: 20px;")
        layout.addWidget(desc)
        
        # 文件选择区域
        file_group = QGroupBox("📁 选择要保护的文件或文件夹")
        file_layout = QVBoxLayout(file_group)
        
        # 输入路径
        input_layout = QHBoxLayout()
        self.input_path = QLineEdit()
        self.input_path.setPlaceholderText("请选择要保护的文件或文件夹...")
        input_layout.addWidget(self.input_path)
        
        self.select_file_btn = QPushButton("选择文件")
        self.select_file_btn.clicked.connect(self.select_file)
        input_layout.addWidget(self.select_file_btn)
        
        self.select_folder_btn = QPushButton("选择文件夹")
        self.select_folder_btn.clicked.connect(self.select_folder)
        input_layout.addWidget(self.select_folder_btn)
        
        file_layout.addLayout(input_layout)
        
        # 输出目录
        output_layout = QHBoxLayout()
        output_layout.addWidget(QLabel("输出目录:"))
        self.output_path = QLineEdit()
        self.output_path.setPlaceholderText("留空则保存到原文件夹...")
        output_layout.addWidget(self.output_path)
        
        self.select_output_btn = QPushButton("选择目录")
        self.select_output_btn.clicked.connect(self.select_output_dir)
        output_layout.addWidget(self.select_output_btn)
        
        file_layout.addLayout(output_layout)
        layout.addWidget(file_group)
        
        # 处理模式
        mode_group = QGroupBox("⚙️ 处理模式")
        mode_layout = QVBoxLayout(mode_group)
        
        self.single_mode = QRadioButton("单文件/文件夹保护")
        self.single_mode.setChecked(True)
        mode_layout.addWidget(self.single_mode)
        
        self.batch_mode = QRadioButton("批量保护文件夹中的文件")
        mode_layout.addWidget(self.batch_mode)
        
        # 文件类型过滤
        filter_layout = QHBoxLayout()
        filter_layout.addWidget(QLabel("文件类型过滤:"))
        self.file_extensions = QLineEdit(".txt .pdf .doc .docx .jpg .png")
        self.file_extensions.setPlaceholderText("用空格分隔，留空表示所有文件")
        filter_layout.addWidget(self.file_extensions)
        mode_layout.addLayout(filter_layout)
        
        layout.addWidget(mode_group)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.protect_btn = QPushButton("🛡️ 开始保护")
        self.protect_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        self.protect_btn.clicked.connect(self.start_protection)
        button_layout.addWidget(self.protect_btn)
        
        self.clear_btn = QPushButton("🗑️ 清空日志")
        self.clear_btn.clicked.connect(self.clear_log)
        button_layout.addWidget(self.clear_btn)
        
        layout.addLayout(button_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 日志区域
        log_group = QGroupBox("📋 操作日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        log_layout.addWidget(self.log_text)
        
        layout.addWidget(log_group)
        
        # 检查依赖
        self.check_dependencies()
    
    def check_dependencies(self):
        """检查依赖"""
        try:
            import pyminizip
            self.log("✅ 检测到pyminizip，将使用强加密保护")
        except ImportError:
            self.log("⚠️ 未检测到pyminizip，将使用较弱的加密方式")
            self.log("💡 建议安装pyminizip获得更强加密: pip install pyminizip")
    
    def log(self, message):
        """添加日志"""
        self.log_text.append(message)
        self.log_text.ensureCursorVisible()
    
    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
    
    def select_file(self):
        """选择文件"""
        file_path, _ = QFileDialog.getOpenFileName(self, "选择要保护的文件")
        if file_path:
            self.input_path.setText(file_path)
    
    def select_folder(self):
        """选择文件夹"""
        folder_path = QFileDialog.getExistingDirectory(self, "选择要保护的文件夹")
        if folder_path:
            self.input_path.setText(folder_path)
    
    def select_output_dir(self):
        """选择输出目录"""
        output_dir = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if output_dir:
            self.output_path.setText(output_dir)
    
    def start_protection(self):
        """开始保护"""
        input_path = self.input_path.text().strip()
        if not input_path:
            QMessageBox.warning(self, "警告", "请选择要保护的文件或文件夹！")
            return
        
        if not os.path.exists(input_path):
            QMessageBox.warning(self, "警告", "选择的路径不存在！")
            return
        
        output_dir = self.output_path.text().strip() or None
        batch_mode = self.batch_mode.isChecked()
        
        # 获取文件扩展名
        extensions = []
        if batch_mode:
            ext_text = self.file_extensions.text().strip()
            if ext_text:
                extensions = ext_text.split()
        
        # 禁用按钮，显示进度条
        self.protect_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 无限进度条
        
        # 启动工作线程
        self.worker = ProtectionWorker(input_path, output_dir, batch_mode, extensions)
        self.worker.progress.connect(self.log)
        self.worker.finished.connect(self.on_protection_finished)
        self.worker.error.connect(self.on_protection_error)
        self.worker.start()
    
    def on_protection_finished(self, output_path, password, encryption_method):
        """保护完成"""
        self.log(f"\n✅ 保护完成！")
        self.log(f"📁 输出文件: {output_path}")
        self.log(f"🔑 解压密码: {password}")
        self.log(f"🔒 加密方式: {encryption_method}")
        self.log(f"📋 密码已保存在压缩包内的文件夹中")
        
        # 恢复界面
        self.protect_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        
        QMessageBox.information(self, "完成", "文件保护完成！请查看日志获取详细信息。")
    
    def on_protection_error(self, error_msg):
        """保护出错"""
        self.log(f"\n❌ 保护失败: {error_msg}")
        
        # 恢复界面
        self.protect_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        
        QMessageBox.critical(self, "错误", f"保护过程中出现错误:\n{error_msg}")


def main():
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("文件防盗保护工具")
    app.setApplicationVersion("1.0")
    
    # 创建主窗口
    window = FileProtectorGUI()
    window.show()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
