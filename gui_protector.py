#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件防盗保护工具 - 图形界面版本
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
from pathlib import Path
from file_protector import create_protected_archive, batch_protect_files


class FileProtectorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("文件防盗保护工具 💖")
        self.root.geometry("600x500")
        
        # 设置样式
        style = ttk.Style()
        style.theme_use('clam')
        
        self.setup_ui()
        
    def setup_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="文件防盗保护工具 🛡️", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 输入文件/文件夹选择
        ttk.Label(main_frame, text="选择要保护的文件或文件夹:").grid(row=1, column=0, sticky=tk.W, pady=5)
        
        input_frame = ttk.Frame(main_frame)
        input_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        self.input_path = tk.StringVar()
        self.input_entry = ttk.Entry(input_frame, textvariable=self.input_path, width=50)
        self.input_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        
        ttk.Button(input_frame, text="选择文件", 
                  command=self.select_file).grid(row=0, column=1, padx=2)
        ttk.Button(input_frame, text="选择文件夹", 
                  command=self.select_folder).grid(row=0, column=2, padx=2)
        
        input_frame.columnconfigure(0, weight=1)
        
        # 输出目录选择
        ttk.Label(main_frame, text="输出目录 (可选):").grid(row=3, column=0, sticky=tk.W, pady=(15, 5))
        
        output_frame = ttk.Frame(main_frame)
        output_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        self.output_path = tk.StringVar()
        self.output_entry = ttk.Entry(output_frame, textvariable=self.output_path, width=50)
        self.output_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        
        ttk.Button(output_frame, text="选择目录", 
                  command=self.select_output_dir).grid(row=0, column=1)
        
        output_frame.columnconfigure(0, weight=1)
        
        # 处理模式选择
        mode_frame = ttk.LabelFrame(main_frame, text="处理模式", padding="10")
        mode_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=15)
        
        self.mode = tk.StringVar(value="single")
        ttk.Radiobutton(mode_frame, text="单文件/文件夹保护", 
                       variable=self.mode, value="single").grid(row=0, column=0, sticky=tk.W)
        ttk.Radiobutton(mode_frame, text="批量保护文件夹中的所有文件", 
                       variable=self.mode, value="batch").grid(row=1, column=0, sticky=tk.W)
        
        # 文件扩展名过滤 (批量模式)
        ext_frame = ttk.Frame(mode_frame)
        ext_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        
        ttk.Label(ext_frame, text="文件类型过滤 (批量模式):").grid(row=0, column=0, sticky=tk.W)
        self.extensions = tk.StringVar(value=".pdf .doc .docx .txt .jpg .png")
        ttk.Entry(ext_frame, textvariable=self.extensions, width=40).grid(row=1, column=0, sticky=(tk.W, tk.E))
        ttk.Label(ext_frame, text="(用空格分隔，留空表示所有文件)", 
                 font=("Arial", 8)).grid(row=2, column=0, sticky=tk.W)
        
        ext_frame.columnconfigure(0, weight=1)
        
        # 操作按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=6, column=0, columnspan=3, pady=20)
        
        self.protect_btn = ttk.Button(button_frame, text="🛡️ 开始保护", 
                                     command=self.start_protection, style="Accent.TButton")
        self.protect_btn.grid(row=0, column=0, padx=10)
        
        ttk.Button(button_frame, text="📋 清空日志", 
                  command=self.clear_log).grid(row=0, column=1, padx=10)
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        # 日志输出
        ttk.Label(main_frame, text="操作日志:").grid(row=8, column=0, sticky=tk.W, pady=(10, 5))
        
        self.log_text = scrolledtext.ScrolledText(main_frame, height=12, width=70)
        self.log_text.grid(row=9, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        
        # 配置网格权重
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(9, weight=1)
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        
    def select_file(self):
        filename = filedialog.askopenfilename(
            title="选择要保护的文件",
            filetypes=[("所有文件", "*.*")]
        )
        if filename:
            self.input_path.set(filename)
            
    def select_folder(self):
        foldername = filedialog.askdirectory(title="选择要保护的文件夹")
        if foldername:
            self.input_path.set(foldername)
            
    def select_output_dir(self):
        dirname = filedialog.askdirectory(title="选择输出目录")
        if dirname:
            self.output_path.set(dirname)
            
    def log(self, message):
        """添加日志信息"""
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        
    def start_protection(self):
        """开始保护文件"""
        input_path = self.input_path.get().strip()
        if not input_path:
            messagebox.showerror("错误", "请选择要保护的文件或文件夹！")
            return
            
        if not os.path.exists(input_path):
            messagebox.showerror("错误", "选择的路径不存在！")
            return
            
        # 在新线程中执行保护操作
        thread = threading.Thread(target=self.protection_worker, args=(input_path,))
        thread.daemon = True
        thread.start()
        
    def protection_worker(self, input_path):
        """保护操作的工作线程"""
        try:
            # 禁用按钮并显示进度条
            self.protect_btn.config(state='disabled')
            self.progress.start()
            
            output_dir = self.output_path.get().strip() or None
            mode = self.mode.get()
            
            self.log(f"开始保护: {input_path}")
            self.log(f"模式: {'批量保护' if mode == 'batch' else '单文件保护'}")
            
            if mode == "batch":
                # 批量保护模式
                extensions_str = self.extensions.get().strip()
                extensions = extensions_str.split() if extensions_str else None
                
                self.log(f"文件类型过滤: {extensions or '所有文件'}")
                
                results = batch_protect_files(input_path, output_dir, extensions)
                
                self.log(f"\n✅ 批量保护完成！共处理 {len(results)} 个文件")
                for result in results:
                    self.log(f"📁 {Path(result['original']).name} → {Path(result['protected']).name}")
                    self.log(f"🔑 密码: {result['password']}")
                    self.log("-" * 50)
                    
            else:
                # 单文件保护模式
                output_file, password = create_protected_archive(input_path, output_dir)
                
                self.log(f"\n✅ 保护完成！")
                self.log(f"📁 输出文件: {output_file}")
                self.log(f"🔑 解压密码: {password}")
                self.log(f"📋 密码已保存在压缩包内的文件夹中")
                
            messagebox.showinfo("完成", "文件保护完成！请查看日志获取详细信息。")
            
        except Exception as e:
            error_msg = f"保护过程中出现错误: {str(e)}"
            self.log(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)
            
        finally:
            # 恢复按钮并停止进度条
            self.progress.stop()
            self.protect_btn.config(state='normal')


def main():
    root = tk.Tk()
    app = FileProtectorGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
