@echo off
chcp 65001 >nul
title 文件防盗保护工具

echo.
echo ==========================================
echo           文件防盗保护工具 🛡️
echo ==========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Python，请先安装Python 3.6+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境正常

echo.
echo 正在检查依赖...
python -c "import PyQt6" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ 缺少PyQt6依赖
    echo 是否现在安装依赖？(Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        echo 正在安装依赖...
        python install_dependencies.py
    ) else (
        echo 取消安装，程序可能无法正常运行
    )
)

echo.
echo 🚀 启动文件防盗保护工具...
python file_protector_qt6.py

if errorlevel 1 (
    echo.
    echo ❌ 程序运行出错
    echo 请检查是否已正确安装所有依赖
    echo.
    pause
)

echo.
echo 程序已退出
pause
