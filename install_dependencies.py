#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装文件防盗保护工具所需的依赖
"""

import subprocess
import sys
import os

def install_package(package_name):
    """安装Python包"""
    try:
        print(f"正在安装 {package_name}...")
        result = subprocess.run([sys.executable, "-m", "pip", "install", package_name], 
                              capture_output=True, text=True, check=True)
        print(f"✅ {package_name} 安装成功！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败:")
        print(f"错误信息: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ 安装 {package_name} 时出现异常: {e}")
        return False

def check_package(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name)
        print(f"✅ {package_name} 已安装")
        return True
    except ImportError:
        print(f"⚠️ {package_name} 未安装")
        return False

def main():
    print("🔧 文件防盗保护工具 - 依赖安装程序")
    print("=" * 50)
    
    # 需要安装的包
    packages = [
        "PyQt6",
        "pyminizip"
    ]
    
    print("检查已安装的包...")
    installed = []
    need_install = []
    
    for package in packages:
        if check_package(package):
            installed.append(package)
        else:
            need_install.append(package)
    
    if not need_install:
        print("\n🎉 所有依赖都已安装，可以直接使用工具！")
        print("运行命令: python file_protector_qt6.py")
        return True
    
    print(f"\n需要安装的包: {', '.join(need_install)}")
    
    # 询问用户是否安装
    response = input("\n是否现在安装这些依赖？(y/n): ").lower().strip()
    if response not in ['y', 'yes', '是']:
        print("取消安装。")
        return False
    
    # 安装缺失的包
    success_count = 0
    for package in need_install:
        if install_package(package):
            success_count += 1
    
    print(f"\n安装结果: {success_count}/{len(need_install)} 个包安装成功")
    
    if success_count == len(need_install):
        print("🎉 所有依赖安装完成！现在可以使用文件防盗保护工具了！")
        print("运行命令: python file_protector_qt6.py")
        return True
    else:
        print("⚠️ 部分依赖安装失败，工具可能无法正常工作")
        print("💡 你可以手动运行:")
        for package in need_install:
            print(f"   pip install {package}")
        return False

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
