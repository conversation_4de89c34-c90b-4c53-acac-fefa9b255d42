# 文件防盗保护工具 🛡️

## 功能介绍

这个工具可以帮你创建特殊的文件夹结构，有效防止文件被批量盗取：

```
原文件名.zip
└── 原文件名/
    ├── 随机名.zip (密码保护的真实文件)
    └── 解压密码：xxxxxxxxx/
        └── 使用说明.txt
```

## 防盗原理

1. **双重保护**：外层无密码，内层有随机密码
2. **随机命名**：内层压缩包使用随机名，无法批量识别
3. **密码隐藏**：密码信息藏在文件夹名中，不易被自动化脚本发现
4. **结构复杂**：需要手动操作才能获取真实文件，阻止批量处理

## 安装要求

- Python 3.6+
- PyQt6 (图形界面)
- pyminizip (强加密，可选但推荐)

## 快速开始

### 方法1：使用批处理文件（推荐）
1. 双击运行 `启动工具.bat`
2. 程序会自动检查环境和依赖
3. 如果缺少依赖会提示安装

### 方法2：手动安装依赖
1. 运行 `python install_dependencies.py`
2. 运行 `python file_protector_qt6.py`

### 方法3：命令行安装
```bash
pip install PyQt6 pyminizip
python file_protector_qt6.py
```

## 使用方法

### 单文件保护
1. 点击"选择文件"选择要保护的文件
2. 选择输出目录（可选）
3. 点击"开始保护"

### 文件夹保护
1. 点击"选择文件夹"选择要保护的文件夹
2. 选择输出目录（可选）
3. 点击"开始保护"

### 批量保护
1. 选择"批量保护文件夹中的文件"
2. 点击"选择文件夹"选择包含多个文件的文件夹
3. 设置文件类型过滤（如：.txt .pdf .doc）
4. 点击"开始保护"

## 解压方法

1. 解压外层压缩包（如：重要文档.zip）
2. 进入解压后的文件夹（如：重要文档/）
3. 查看"解压密码：xxxxxxx"文件夹中的密码
4. 使用该密码解压随机名的压缩包
5. 获得原始文件

## 安全特性

- ✅ 随机生成12位强密码
- ✅ 内层压缩包使用随机文件名
- ✅ 密码信息隐藏在文件夹名中
- ✅ 支持强加密（pyminizip）和标准加密（zipfile）
- ✅ 完整的错误处理和用户提示
- ✅ 批量处理生成详细报告

## 注意事项

⚠️ **重要提醒**：
- 请妥善保管生成的密码，丢失后无法恢复文件
- 建议备份原始文件
- 密码信息会保存在压缩包内的说明文件中
- 批量处理时会生成详细报告文件

## 技术特点

- 使用Qt6现代化界面
- 多线程处理，界面不卡顿
- 支持中文文件名，无乱码问题
- 自动检测和安装依赖
- 完整的错误处理机制

## 示例输出

```
正在保护: 重要文档.pdf
✅ 保护完成！
📁 输出文件: D:\Documents\重要文档.zip
🔑 解压密码: K9mX#7nQ$2vB
🔒 加密方式: 使用pyminizip加密成功
📋 密码已保存在压缩包内的文件夹中
```

## 故障排除

### 问题1：程序无法启动
- 检查Python版本是否为3.6+
- 运行 `python install_dependencies.py` 安装依赖

### 问题2：中文文件名乱码
- 确保使用UTF-8编码
- 本工具已处理编码问题，应该不会出现乱码

### 问题3：密码保护无效
- 确保安装了pyminizip：`pip install pyminizip`
- 如果仍有问题，程序会使用备用加密方式

### 问题4：长文件名处理失败
- 本工具已优化长文件名处理
- 如果仍有问题，请尝试重命名为较短的文件名

## 更新日志

### v1.0
- 初始版本
- 支持单文件和文件夹保护
- 支持批量处理
- Qt6现代化界面
- 完善的错误处理

---

💖 希望这个工具能帮助你保护重要文件，让那些坏人无法批量盗取！
