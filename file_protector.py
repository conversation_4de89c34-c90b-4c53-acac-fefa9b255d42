#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件防盗保护工具
创建嵌套压缩包结构，防止批量盗取文件
"""

import os
import zipfile
import random
import string
import shutil
import tempfile
import subprocess
import sys
from pathlib import Path
import argparse


def generate_random_password(length=12):
    """生成随机密码（避免Windows文件名禁用字符）"""
    # 避免在文件夹名中使用的特殊字符：< > : " | ? * \ /
    characters = string.ascii_letters + string.digits + "!@#$%&"
    return ''.join(random.choice(characters) for _ in range(length))


def generate_random_filename(extension=".zip"):
    """生成随机文件名"""
    random_name = ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(16))
    return random_name + extension


def create_protected_archive(input_path, output_dir=None):
    """
    创建受保护的压缩包
    
    Args:
        input_path: 要保护的文件或文件夹路径
        output_dir: 输出目录，默认为当前目录
    
    Returns:
        tuple: (输出文件路径, 解压密码)
    """
    input_path = Path(input_path)
    if not input_path.exists():
        raise FileNotFoundError(f"输入路径不存在: {input_path}")
    
    if output_dir is None:
        output_dir = Path.cwd()
    else:
        output_dir = Path(output_dir)
    
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 生成随机密码
    password = generate_random_password()
    
    # 生成随机内部压缩包名
    inner_zip_name = generate_random_filename()
    
    # 创建临时工作目录
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 第一步：创建内部加密压缩包
        inner_zip_path = temp_path / inner_zip_name

        print(f"正在创建内部压缩包: {inner_zip_name}")

        # 尝试使用pyminizip创建加密压缩包
        try:
            import pyminizip

            if input_path.is_file():
                # 单文件压缩 - 先复制到临时位置以避免路径问题
                temp_file_path = temp_path / "temp_file"
                shutil.copy2(input_path, temp_file_path)

                # 使用简单的文件名进行压缩
                pyminizip.compress(str(temp_file_path), input_path.name, str(inner_zip_path), password, 5)
            else:
                # 文件夹压缩 - 先复制到临时位置
                temp_folder_path = temp_path / "temp_folder"
                shutil.copytree(input_path, temp_folder_path)

                # 创建文件列表
                file_list = []
                prefix_list = []
                for file_path in temp_folder_path.rglob('*'):
                    if file_path.is_file():
                        file_list.append(str(file_path))
                        # 保持相对路径结构
                        rel_path = file_path.relative_to(temp_folder_path)
                        prefix_list.append(str(rel_path.parent) if rel_path.parent != Path('.') else "")

                if file_list:
                    pyminizip.compress_multiple(file_list, prefix_list, str(inner_zip_path), password, 5)
                else:
                    raise Exception("文件夹为空")

        except ImportError:
            # 如果没有pyminizip，使用zipfile创建普通压缩包，然后在说明中提醒用户
            print("⚠️ 未安装pyminizip，将创建无密码的内层压缩包")
            print("💡 建议安装pyminizip以获得更好的加密效果: pip install pyminizip")

            with zipfile.ZipFile(inner_zip_path, 'w', zipfile.ZIP_DEFLATED) as inner_zip:
                if input_path.is_file():
                    inner_zip.write(input_path, input_path.name)
                else:
                    for file_path in input_path.rglob('*'):
                        if file_path.is_file():
                            arcname = file_path.relative_to(input_path.parent)
                            inner_zip.write(file_path, arcname)
        
        # 第二步：创建密码提示文件夹（限制文件夹名长度）
        # Windows文件夹名最大255字符，我们限制在50字符以内
        safe_password = password.replace('\\', '').replace('/', '').replace(':', '').replace('*', '').replace('?', '').replace('"', '').replace('<', '').replace('>', '').replace('|', '')
        password_folder_name = f"解压密码-{safe_password}"
        if len(password_folder_name) > 50:
            password_folder_name = f"解压密码-{safe_password[:30]}"

        password_folder_path = temp_path / password_folder_name
        password_folder_path.mkdir()
        
        # 在密码文件夹中创建一个说明文件
        readme_path = password_folder_path / "使用说明.txt"
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(f"解压密码：{password}\n\n")
            f.write("使用方法：\n")
            f.write(f"1. 使用上述密码解压 {inner_zip_name}\n")
            f.write("2. 即可获得原始文件\n\n")
            f.write("注意：请妥善保管此密码，丢失后无法恢复文件！")
        
        # 第三步：创建最终的外层压缩包
        base_name = input_path.stem if input_path.is_file() else input_path.name
        final_zip_name = f"{base_name}_protected.zip"
        final_zip_path = output_dir / final_zip_name
        
        print(f"正在创建最终压缩包: {final_zip_name}")
        with zipfile.ZipFile(final_zip_path, 'w', zipfile.ZIP_DEFLATED) as final_zip:
            # 添加内部压缩包
            final_zip.write(inner_zip_path, inner_zip_name)
            
            # 添加密码文件夹及其内容
            for file_path in password_folder_path.rglob('*'):
                if file_path.is_file():
                    arcname = file_path.relative_to(temp_path)
                    final_zip.write(file_path, arcname)
    
    print(f"✅ 保护完成！")
    print(f"📁 输出文件: {final_zip_path}")
    print(f"🔑 解压密码: {password}")
    print(f"📋 密码已保存在压缩包内的文件夹中")
    
    return str(final_zip_path), password


def batch_protect_files(input_dir, output_dir=None, file_extensions=None):
    """
    批量保护文件夹中的文件
    
    Args:
        input_dir: 输入目录
        output_dir: 输出目录
        file_extensions: 要处理的文件扩展名列表，如 ['.pdf', '.doc', '.txt']
    """
    input_dir = Path(input_dir)
    if not input_dir.exists():
        raise FileNotFoundError(f"输入目录不存在: {input_dir}")
    
    if output_dir is None:
        output_dir = input_dir / "protected_files"
    else:
        output_dir = Path(output_dir)
    
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 获取要处理的文件
    files_to_process = []
    if file_extensions:
        for ext in file_extensions:
            files_to_process.extend(input_dir.glob(f"*{ext}"))
    else:
        files_to_process = [f for f in input_dir.iterdir() if f.is_file()]
    
    results = []
    for file_path in files_to_process:
        try:
            print(f"\n处理文件: {file_path.name}")
            output_path, password = create_protected_archive(file_path, output_dir)
            results.append({
                'original': str(file_path),
                'protected': output_path,
                'password': password
            })
        except Exception as e:
            print(f"❌ 处理文件 {file_path.name} 时出错: {e}")
    
    # 生成批量处理报告
    report_path = output_dir / "protection_report.txt"
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("文件保护报告\n")
        f.write("=" * 50 + "\n\n")
        for result in results:
            f.write(f"原文件: {result['original']}\n")
            f.write(f"保护文件: {result['protected']}\n")
            f.write(f"解压密码: {result['password']}\n")
            f.write("-" * 30 + "\n")
    
    print(f"\n✅ 批量处理完成！共处理 {len(results)} 个文件")
    print(f"📋 详细报告已保存至: {report_path}")
    
    return results


def main():
    parser = argparse.ArgumentParser(description="文件防盗保护工具")
    parser.add_argument("input", help="输入文件或文件夹路径")
    parser.add_argument("-o", "--output", help="输出目录")
    parser.add_argument("-b", "--batch", action="store_true", help="批量处理模式")
    parser.add_argument("-e", "--extensions", nargs="+", help="批量模式下要处理的文件扩展名")
    
    args = parser.parse_args()
    
    try:
        if args.batch:
            batch_protect_files(args.input, args.output, args.extensions)
        else:
            create_protected_archive(args.input, args.output)
    except Exception as e:
        print(f"❌ 错误: {e}")


if __name__ == "__main__":
    main()
